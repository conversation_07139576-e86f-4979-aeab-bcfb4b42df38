# 洗钱机管理员设置指南

## 如何设置管理员权限

洗钱机插件现在使用独立的权限系统，不再依赖 `boii_utils`。

### 1. 获取玩家标识符

要设置管理员权限，你需要获取玩家的标识符。有以下几种方式：

#### 方法一：使用命令获取（推荐）
在服务器控制台或游戏内执行以下命令来获取玩家的标识符：

```lua
-- 在服务器控制台执行
/players
```

或者添加这个临时命令到任何服务器脚本中：
```lua
RegisterCommand('getmyid', function(source, args, raw)
    local identifiers = GetPlayerIdentifiers(source)
    for _, id in pairs(identifiers) do
        print(GetPlayerName(source) .. " - " .. id)
    end
end, false)
```

#### 方法二：查看服务器日志
玩家连接时，服务器日志通常会显示他们的标识符。

### 2. 配置管理员权限

编辑 `washers/config.lua` 文件中的 `config.admin_identifiers` 部分：

```lua
config.admin_identifiers = {
    'steam:110000100000000',  -- 替换为实际的Steam ID
    'license:xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',  -- 替换为实际的License
    'discord:123456789012345678',  -- 替换为实际的Discord ID
    -- 添加更多管理员...
}
```

### 3. 标识符类型说明

- **Steam ID**: 格式为 `steam:xxxxxxxxxxxxxxxxx`
- **License**: 格式为 `license:xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
- **Discord ID**: 格式为 `discord:xxxxxxxxxxxxxxxxxx`

### 4. 特殊设置

如果你想允许所有人使用管理员命令（不推荐），可以设置：
```lua
config.admin_identifiers = { 'all' }
```

### 5. 可用的管理员命令

- `/moneywash:admin` - 打开洗钱机配置菜单
- `/moneywash:place_washer` - 放置新的洗钱机

### 6. 重启资源

修改配置后，重启洗钱机资源：
```
restart boii_moneywash
```

## 注意事项

1. 确保标识符格式正确
2. 标识符区分大小写
3. 修改配置后需要重启资源
4. 建议只给信任的玩家管理员权限
