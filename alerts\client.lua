--- @section Functions

--- Create a blip with customizable alpha and duration, and optional color palette.
--- @param options table: A table containing the blip data.
local function create_blip_alpha(options)
    local blip = AddBlipForCoord(options.coords)
    SetBlipSprite(blip, options.sprite)
    SetBlipColour(blip, options.colour)
    SetBlipScale(blip, options.scale)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentString(options.label)
    EndTextCommandSetBlipName(blip)
    local alpha = options.alpha
    local duration = options.duration * 1000
    local start_time = GetGameTimer()
    local step_time = 100
    local steps = duration / step_time
    local alpha_decrement = alpha / steps
    local colour_palette = options.colour_palette or {options.colour}
    local current_color_index = 1
    CreateThread(function()
        while alpha > 0 do
            alpha = alpha - alpha_decrement
            if alpha <= 0 then
                RemoveBlip(blip)
                return
            end
            SetBlipAlpha(blip, math.floor(alpha))
            Wait(step_time)
        end
    end)
    CreateThread(function()
        while alpha > 0 do
            if #colour_palette > 1 then
                current_color_index = current_color_index % #colour_palette + 1
                SetBlipColour(blip, colour_palette[current_color_index])
            end
            Wait(750)
        end
    end)
end

--- @section Events

--- Sends notification, plays sound, and creates blip on alert.
--- @param player_coords vector: Coords for player who triggered alert.
--- @param label string: Label to display on alert.
RegisterNetEvent('boii_moneywash:cl:alert_police', function(wash_coords, label)
    NOTIFICATIONS.send({
        type = 'primary',
        header = 'MONEY WASH',
        message = label,
        duration = 5000
    })
    PlaySound(-1, "Lose_1st", "GTAO_FM_Events_Soundset", 0, 0, 1)
    create_blip_alpha({
        coords = vector3(wash_coords.x, wash_coords.y, wash_coords.z),
        sprite = 161,
        colour = 1, 
        scale = 1.5, 
        label = label,
        alpha = 250,
        duration = 20,
        colour_palette = {1, 29} 
    })
end)