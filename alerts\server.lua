-- 延迟获取配置，避免在文件顶部访问 config
local function get_police_config()
    return config.police.jobs, config.police.on_duty_only
end

--- Gets players with police jobs.
--- @param job_table table: Table of jobs to check.
--- @param on_duty boolean: Indicates if only on-duty police should be counted.
--- @return table: Table of player sources with the specified jobs.
local function get_police_players(job_table, on_duty)
    local duty = on_duty or false
    local players = CORE.get_players()
    if not players then print('No players found') return end
    local jobs = job_table
    local players_with_jobs = {}
    for _, player in pairs(players) do
        local player_source = type(player) == 'table' and player.source or player
        if player_source then
            local player_has_job = CORE.player_has_job(player_source, jobs, duty)
            if player_has_job then 
                players_with_jobs[#players_with_jobs + 1] = player_source
            end
        end
    end
    return players_with_jobs
end

--- Sends an alert to the all connected police players.
--- @param coords table: Coords of the wash.
function alert_police(coords)
    local police_jobs, on_duty_only = get_police_config()
    local police = get_police_players(police_jobs, on_duty_only)
    local label = '10-33 | Suspicious Activity'
    for _, police_player in pairs(police) do
        TriggerClientEvent('boii_moneywash:cl:alert_police', police_player, coords, label)
    end
end