# V1.0.2
```diff
### Key
! Modified
- Removed
+ Added
###

! Switched from key_utils to boii_utils v2.0
```


# V1.0.1

```diff
### Key
! Modified
- Removed
+ Added
###

> Updated fully to with new key_utils, will not work with old fivem_utils some key functions have changed.
+ Support for markedbills is now done
```

# V1.0.0

```diff
### Key
! Modified
- Removed
+ Added
###

- removed left over of debug spam prints.

! Changed how placement keys are handled, now pulls key list once from utils instead of for each key and keys are mapped more efficiently.
! admin ranks now include member rank by default if you dont want members to be able to use remove this.

+ `*.lua` added to fxmanifest as loader file was not viewable due to escrow ignore.
+ dependencies added to manifest should stop load issues for people.
```