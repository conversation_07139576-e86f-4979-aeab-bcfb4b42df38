--- Store resource name for later validation.
RESOURCE_NAME = GetCurrentResourceName()

--- @section 独立模块实现（不依赖 boii_utils）

--- 简单的表格工具模块
TABLES = {
    contains = function(table, element)
        for _, value in pairs(table) do
            if value == element then
                return true
            end
        end
        return false
    end
}

--- 简单的通知模块
NOTIFICATIONS = {
    send = function(source, data)
        -- 这里可以根据你的通知系统进行调整
        -- 示例：使用基础的聊天消息
        if data.type == 'error' then
            TriggerClientEvent('chat:addMessage', source, {
                args = { '^1[' .. (data.header or 'ERROR') .. ']', data.message }
            })
        elseif data.type == 'success' then
            TriggerClientEvent('chat:addMessage', source, {
                args = { '^2[' .. (data.header or 'SUCCESS') .. ']', data.message }
            })
        else
            TriggerClientEvent('chat:addMessage', source, {
                args = { '[' .. (data.header or 'INFO') .. ']', data.message }
            })
        end
    end
}

--- 简单的核心模块（需要根据你的框架调整）
CORE = {
    -- 这些函数需要根据你使用的框架（ESX/QB等）进行实现
    get_balance_by_type = function(source, balance_type)
        -- 示例：ESX框架实现
        if GetResourceState('es_extended') == 'started' then
            local ESX = exports['es_extended']:getSharedObject()
            local xPlayer = ESX.GetPlayerFromId(source)
            if not xPlayer then return false end

            if balance_type == 'cash' or balance_type == 'money' then
                return xPlayer.getMoney()
            else
                local account = xPlayer.getAccount(balance_type)
                return account and account.money or 0
            end
        end
        -- 如果不是ESX，返回默认值或实现其他框架的逻辑
        return 1000 -- 默认值，请根据实际情况修改
    end,

    remove_balance = function(source, balance_type, amount)
        if GetResourceState('es_extended') == 'started' then
            local ESX = exports['es_extended']:getSharedObject()
            local xPlayer = ESX.GetPlayerFromId(source)
            if not xPlayer then return false end

            if balance_type == 'cash' or balance_type == 'money' then
                xPlayer.removeMoney(amount)
            else
                xPlayer.removeAccountMoney(balance_type, amount)
            end
        end
    end,

    add_balance = function(source, balance_type, amount)
        if GetResourceState('es_extended') == 'started' then
            local ESX = exports['es_extended']:getSharedObject()
            local xPlayer = ESX.GetPlayerFromId(source)
            if not xPlayer then return false end

            if balance_type == 'cash' or balance_type == 'money' then
                xPlayer.addMoney(amount)
            else
                xPlayer.addAccountMoney(balance_type, amount)
            end
        end
    end,

    has_item = function(source, item_name, amount)
        amount = amount or 1
        if GetResourceState('es_extended') == 'started' then
            local ESX = exports['es_extended']:getSharedObject()
            local xPlayer = ESX.GetPlayerFromId(source)
            if not xPlayer then return false end

            local item = xPlayer.getInventoryItem(item_name)
            return item and item.count >= amount
        end
        return false
    end,

    remove_item = function(source, item_name, amount)
        if GetResourceState('es_extended') == 'started' then
            local ESX = exports['es_extended']:getSharedObject()
            local xPlayer = ESX.GetPlayerFromId(source)
            if not xPlayer then return false end

            xPlayer.removeInventoryItem(item_name, amount)
        end
    end,

    add_item = function(source, item_name, amount, metadata)
        if GetResourceState('es_extended') == 'started' then
            local ESX = exports['es_extended']:getSharedObject()
            local xPlayer = ESX.GetPlayerFromId(source)
            if not xPlayer then return false end

            xPlayer.addInventoryItem(item_name, amount)
        end
    end,

    get_inventory = function(source)
        if GetResourceState('es_extended') == 'started' then
            local ESX = exports['es_extended']:getSharedObject()
            local xPlayer = ESX.GetPlayerFromId(source)
            if not xPlayer then return false end

            return xPlayer.getInventory()
        end
        return {}
    end,

    player_has_job = function(source, jobs, on_duty_only)
        if GetResourceState('es_extended') == 'started' then
            local ESX = exports['es_extended']:getSharedObject()
            local xPlayer = ESX.GetPlayerFromId(source)
            if not xPlayer then return false end

            local playerJob = xPlayer.getJob()
            for _, job in pairs(jobs) do
                if playerJob.name == job then
                    return true
                end
            end
        end
        return false
    end,

    register_item = function(item_name, callback)
        -- 简单的物品注册，可以根据需要扩展
        RegisterServerEvent('boii_moneywash:use_item_' .. item_name)
        AddEventHandler('boii_moneywash:use_item_' .. item_name, function()
            callback(source)
        end)
    end
}

--- @section Global Player Variables

is_placing = false