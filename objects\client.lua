--- Max distance from player you can place a new washer.
--- Increase this if you want to place them further away from your current standing position. 
local MAX_PLACEMENT_DISTANCE = 50.0

--- Stores all active objects.
local object_data = {}

--- Stores current preview object and data.
local preview_object = nil
local preview_data = nil

local KEYS = KEYS.get_keys()

local key_list = {
    enter = { name = "Enter", label = "Confirm" },
    backspace = { name = "Backspace", label = "Cancel" },
    w = { name = "W", label = "Move Forward" },
    s = { name = "S", label = "Move Backward" },
    a = { name = "A", label = "Move Left" },
    d = { name = "D", label = "Move Right" },
    g = { name = "G", label = "Rotate Left" },
    h = { name = "H", label = "Rotate Right" },
    arrowup = { name = "Arrow Up", label = "Camera Up" },
    arrowdown = { name = "Arrow Down", label = "Camera Down" },
    arrowleft = { name = "Arrow Left", label = "Camera Left" },
    arrowright = { name = "<PERSON> Right", label = "Camera Right" }
}

local placement_actions = {}

for key, data in pairs(key_list) do
    placement_actions[#placement_actions + 1] = {
        key = KEYS[key],
        name = data.name,
        label = data.label,
        action = function(params)
            if key == "enter" and preview_data then confirm_object_placement(preview_data) end
            if key == "backspace" then cancel_object_placement() end
            if key == "w" then
                local new_coords = params.object_coords - params.cam_forward * 0.02
                if #(new_coords - GetEntityCoords(PlayerPedId())) <= MAX_PLACEMENT_DISTANCE then
                    params.object_coords = new_coords
                end
            end
            if key == "s" then
                local new_coords = params.object_coords + params.cam_forward * 0.02
                if #(new_coords - GetEntityCoords(PlayerPedId())) <= MAX_PLACEMENT_DISTANCE then
                    params.object_coords = new_coords
                end
            end
            if key == "a" then
                local new_coords = params.object_coords - params.cam_right * 0.02
                if #(new_coords - GetEntityCoords(PlayerPedId())) <= MAX_PLACEMENT_DISTANCE then
                    params.object_coords = new_coords
                end
            end
            if key == "d" then
                local new_coords = params.object_coords + params.cam_right * 0.02
                if #(new_coords - GetEntityCoords(PlayerPedId())) <= MAX_PLACEMENT_DISTANCE then
                    params.object_coords = new_coords
                end
            end
            if key == "g" then params.heading = params.heading - 0.25 end
            if key == "h" then params.heading = params.heading + 0.25 end
            if key == "arrowup" then params.camera_angle_vertical = math.min(params.camera_angle_vertical + 0.5, 89.0) end
            if key == "arrowdown" then params.camera_angle_vertical = math.max(params.camera_angle_vertical - 0.5, -89.0) end
            if key == "arrowleft" then params.camera_angle_horizontal = params.camera_angle_horizontal - 1.0 end
            if key == "arrowright" then params.camera_angle_horizontal = params.camera_angle_horizontal + 1.0 end
        end
    }
end

--- @section Internal Functions

--- Requests updated washers on load.
local function request_washers()
    TriggerServerEvent('boii_moneywash:sv:request_washers')
end
request_washers()

--- Disable all controls except those needed for placement.
local function disable_all_controls()
    DisableAllControlActions(0)
    for _, action in ipairs(placement_actions) do
        EnableControlAction(0, action.key, true)
    end
end

--- Attach a camera to the object with an offset.
local function attach_placement_camera(object)
    if DoesCamExist(placement_camera) then
        DestroyCam(placement_camera, false)
    end
    placement_camera = CreateCam("DEFAULT_SCRIPTED_CAMERA", true)
    local obj_coords = GetEntityCoords(object)
    SetCamCoord(placement_camera, obj_coords.x - 2.0, obj_coords.y - 2.0, obj_coords.z + 1.5)
    PointCamAtEntity(placement_camera, object)
    SetCamActive(placement_camera, true)
    RenderScriptCams(true, false, 0, true, false)
end

--- Destroy the placement camera.
local function destroy_placement_camera()
    if DoesCamExist(placement_camera) then
        RenderScriptCams(false, false, 0, true, false)
        DestroyCam(placement_camera, false)
        placement_camera = nil
    end
end

--- Function to display UI instructions.
local function show_placement_ui()
    local filtered_controls = {}
    for _, action in ipairs(placement_actions) do
        filtered_controls[#filtered_controls + 1] = { label = action.label, name = action.name }
    end
    SendNUIMessage({ action = 'open_prop_placer', controls = filtered_controls })
end

--- Function to hide UI instructions.
local function hide_placement_ui()
    SendNUIMessage({ action = 'close_prop_placer' })
end

--- Create and manage an object in the world.
--- @param data table: Object configuration data.
--- @param is_placing boolean: Whether the object is in placement mode.
--- @param callback function: Function to call once the process is complete.
local function create_object(data, is_placing, callback)
    local model = (type(data.model) == 'number' and data.model or GetHashKey(data.model))
    REQUESTS.model(model)
    local object_id = data.id
    local ground_z = data.coords.z
    local found_ground, z = GetGroundZFor_3dCoord(data.coords.x, data.coords.y, data.coords.z, false)
    if found_ground then
        ground_z = z
    end
    local obj = CreateObject(model, data.coords.x, data.coords.y, ground_z, false, false, data.networked or false)
    SetEntityHeading(obj, data.coords.w or 0.0)
    if is_placing then
        SetEntityAlpha(obj, 204, false)
        SetEntityCollision(obj, false, false)
        object_data[object_id] = { object = obj, data = data, is_placing = true }
    else
        FreezeEntityPosition(obj, true)
        object_data[object_id] = { object = obj, data = data, is_placing = false }
        add_zone(data)
    end
    if callback then
        callback(object_id)
    end
    return obj
end

--- Remove an object by its ID.
--- @param id string: Object ID.
local function remove_object(id)
    if object_data[id] then
        DeleteObject(object_data[id].object)
        object_data[id] = nil
    end
end

--- @section Global Functions

--- Start placing an object in preview mode.
--- @param data table: Object data for placement.
function start_object_placement(data)
    if is_placing then print('[Placement] Already placing an object.') return end
    FreezeEntityPosition(PlayerPedId(), true)
    preview_object = create_object(data, true)
    preview_data = data
    is_placing = true
    show_placement_ui()
    attach_placement_camera(preview_object)
end

--- Cancel object placement.
function cancel_object_placement()
    if not is_placing then return end
    if DoesEntityExist(preview_object) then
        DeleteObject(preview_object)
    end
    FreezeEntityPosition(PlayerPedId(), false)
    preview_object = nil
    preview_data = nil
    is_placing = false
    hide_placement_ui()
    destroy_placement_camera()
end

--- Confirm and place the object with appropriate animation.
--- @param data table: Object data for placement.
function confirm_object_placement(data)
    if not is_placing or not preview_object then return end
    local player_ped = PlayerPedId()
    local coords = GetEntityCoords(preview_object)
    local heading = GetEntityHeading(preview_object)
    DeleteObject(preview_object)
    FreezeEntityPosition(player_ped, false)
    preview_object = nil
    preview_data = nil
    hide_placement_ui()
    destroy_placement_camera()
    data.coords = { x = coords.x, y = coords.y, z = coords.z, w = heading }
    data.alpha = nil
    data.networked = true
    TriggerServerEvent('boii_moneywash:sv:save_object', data)
end

--- @section NUI Callbacks

--- Removes nui focus from admin menu.
RegisterNUICallback('remove_ui_focus', function()
    SetNuiFocus(false, false)
end)

--- @section Events

--- Event to spawn an new washer.
--- @param data table: Object data to spawn.
RegisterNetEvent('boii_moneywash:cl:spawn_object', function(data)
    --- Done with slight callback as DUI was buggin out.
    create_object(data, false, function()
        SetTimeout(500, function()
            is_placing = false
        end)
    end)
end)

--- Handle server-triggered object removal.
RegisterNetEvent('boii_moneywash:cl:remove_object', function(id)
    remove_object(id)
end)

--- Handle server triggered object placement.
--- @param data table: Object data for placement.
RegisterNetEvent('boii_moneywash:cl:start_object_placement', function(data)
    start_object_placement(data)
end)

--- Sends NUI message to open washer config.
--- @param data table: Washers data for admin settings.
RegisterNetEvent('boii_moneywash:cl:admin', function(data)
    if not data then return end
    SetNuiFocus(true, true)
    SendNUIMessage({ action = 'open_washer_config', washers = data })
end)

--- @section Threads

--- Handles object placement.
CreateThread(function()
    local camera_radius = 3.0
    local camera_angle_horizontal = 0.0
    local camera_angle_vertical = 15.0
    while true do
        Wait(0)
        if is_placing and DoesEntityExist(preview_object) then
            disable_all_controls()
            local object_coords = GetEntityCoords(preview_object)
            local heading = GetEntityHeading(preview_object)
            local cam_forward = vector3(math.cos(math.rad(camera_angle_horizontal)) * math.cos(math.rad(camera_angle_vertical)), math.sin(math.rad(camera_angle_horizontal)) * math.cos(math.rad(camera_angle_vertical)), 0)
            local cam_right = vector3(-math.sin(math.rad(camera_angle_horizontal)), math.cos(math.rad(camera_angle_horizontal)), 0)
            cam_forward = cam_forward / #cam_forward
            cam_right = cam_right / #cam_right
            local ground_z = object_coords.z
            local found_ground, z = GetGroundZFor_3dCoord(object_coords.x, object_coords.y, object_coords.z + 2.0, false)
            if found_ground then
                ground_z = z
            end
            object_coords = vector3(object_coords.x, object_coords.y, ground_z)
            local params = { object_coords = object_coords, heading = heading, cam_forward = cam_forward, cam_right = cam_right, camera_angle_horizontal = camera_angle_horizontal, camera_angle_vertical = camera_angle_vertical }
            for _, action in ipairs(placement_actions) do
                if IsDisabledControlPressed(0, action.key) and action.action then
                    action.action(params)
                end
            end
            camera_angle_horizontal = params.camera_angle_horizontal
            camera_angle_vertical = params.camera_angle_vertical
            SetEntityCoords(preview_object, params.object_coords.x, params.object_coords.y, params.object_coords.z, false, false, false, false)
            SetEntityHeading(preview_object, params.heading)
            if DoesCamExist(placement_camera) then
                local cam_x = params.object_coords.x + camera_radius * math.cos(math.rad(camera_angle_horizontal)) * math.cos(math.rad(camera_angle_vertical))
                local cam_y = params.object_coords.y + camera_radius * math.sin(math.rad(camera_angle_horizontal)) * math.cos(math.rad(camera_angle_vertical))
                local cam_z = params.object_coords.z + camera_radius * math.sin(math.rad(camera_angle_vertical))
                SetCamCoord(placement_camera, cam_x, cam_y, cam_z)
                PointCamAtEntity(placement_camera, preview_object)
            end
        end
    end
end)

--- @section Clean Up

--- Clean up objects on resource stop.
AddEventHandler('onResourceStop', function(resource)
    if resource ~= GetCurrentResourceName() then return end
    for id, obj in pairs(object_data) do
        if DoesEntityExist(obj.object) then
            DeleteObject(obj.object)
        end
    end
    preview_object = nil
    object_data = {}
    is_placing = false
end)