placed_objects = {} -- Table to track all placed objects

--- Save a New Money Washer
local function save_moneywash(coords)
    MySQL.insert(
        'INSERT INTO money_washers (coords, dirty_in, clean_out, is_washing, remaining_time, is_washed, capacity, max_capacity, max_limit, limit_used, limit_interval, limit_hit_timestamp, take_value, return_value, wash_time) VALUES (@coords, @dirty_in, @clean_out, @is_washing, @remaining_time, @is_washed, @capacity, @max_capacity, @max_limit, @limit_used, @limit_interval, @limit_hit_timestamp, @take_value, @return_value, @wash_time)',
        {
            ['@coords'] = json.encode(coords),
            ['@dirty_in'] = 0,
            ['@clean_out'] = 0,
            ['@is_washing'] = 0,
            ['@remaining_time'] = 0,
            ['@is_washed'] = 0,
            ['@capacity'] = 0,
            ['@max_capacity'] = config.defaults.max_capacity,
            ['@max_limit'] = config.defaults.max_limit,
            ['@limit_used'] = 0,
            ['@limit_interval'] = config.defaults.limit_interval,
            ['@limit_hit_timestamp'] = nil,
            ['@take_value'] = config.defaults.take_value,
            ['@return_value'] = config.defaults.return_value,
            ['@wash_time'] = config.defaults.wash_time
        },
        function(inserted_id)
            if inserted_id then
                print(('[Server] Money wash saved with ID: %d'):format(inserted_id))
            else
                print('[Server] Failed to save money wash to database.')
            end
        end
    )
end

--- Initialize Existing Washers from the Database
local function initialize_washers(source)
    local result = MySQL.query.await('SELECT * FROM money_washers')
    if not result or #result == 0 then
        print('[boii_moneywash] No washers found in the database.')
        return
    end
    for _, washer in ipairs(result) do
        local washer_data = {
            id = washer.id,
            model = config.prop.model,
            coords = json.decode(washer.coords),
            header = config.prop.header,
            icon = config.prop.icon,
            keys = config.actions,
            outline = config.prop.outline,
            capacity = washer.capacity or 0,
            dirty_in = washer.dirty_in or 0,
            clean_out = washer.clean_out or 0,
            max_capacity = washer.max_capacity or config.defaults.max_capacity,
            max_limit = washer.max_limit or config.defaults.max_limit,
            limit_used = washer.limit_used or 0,
            limit_interval = washer.limit_interval or config.defaults.limit_interval,
            take_value = washer.take_value or config.defaults.take_value,
            return_value = washer.return_value or config.defaults.return_value,
            wash_time = washer.wash_time or config.defaults.wash_time,
            is_washing = washer.is_washing == 1,
            is_washed = washer.is_washed == 1,
            remaining_time = washer.remaining_time or 0,
            limit_hit_timestamp = washer.limit_hit_timestamp,
            is_disabled = washer.is_disabled == 1,
            created = washer.created
        }
        placed_objects[washer.id] = washer_data
        TriggerClientEvent('boii_moneywash:cl:spawn_object', source, washer_data)
        if washer.is_washing == 1 and washer.remaining_time > 0 then
            TriggerEvent('boii_moneywash:sv:start_timer', washer.id, washer.remaining_time)
        end
    end
end

--- Start washing timer for a washer
--- @param wash_id number: Washer id.
--- @param remaining_time number: Remaining wash time left on washer.
RegisterServerEvent('boii_moneywash:sv:start_timer', function(wash_id, remaining_time)
    local wash = placed_objects[wash_id]
    if not wash or not wash.is_washing then return end
    local interval = math.floor(remaining_time / 5)
    CreateThread(function()
        while remaining_time > 0 do
            Wait(1000)
            remaining_time = remaining_time - 1
            wash.remaining_time = remaining_time
            if remaining_time % interval == 0 then
                if math.random(1, 100) <= config.police.alert_chance then
                    alert_police(wash.coords)
                end
            end
        end
        wash.is_washing = false
        wash.is_washed = true
        wash.capacity = math.floor(wash.capacity * config.defaults.return_value / 100)
        placed_objects[wash_id] = wash
        TriggerClientEvent('boii_moneywash:cl:sync_dui_data', -1, wash_id, {
            is_washing = false,
            is_washed = true,
            remaining_time = 0,
            capacity = wash.capacity,
        })
    end)
end)

--- Requests washers for the client.
RegisterServerEvent('boii_moneywash:sv:request_washers', function()
    local src = source
    initialize_washers(source)
end)

--- Save object data from the client and handle money wash saving
RegisterServerEvent('boii_moneywash:sv:save_object', function(data)
    local src = source
    placed_objects[data.id] = data
    save_moneywash(data.coords)
    TriggerClientEvent('boii_moneywash:cl:spawn_object', -1, data)
end)

--- Save Washers on Resource Stop
AddEventHandler('onResourceStop', function(resource_name)
    if resource_name ~= GetCurrentResourceName() then return end
    for id, wash in pairs(placed_objects) do
        MySQL.update('UPDATE money_washers SET dirty_in = @dirty_in, clean_out = @clean_out, is_washing = @is_washing, remaining_time = @remaining_time, is_washed = @is_washed, capacity = @capacity, max_capacity = @max_capacity, max_limit = @max_limit, limit_used = @limit_used, limit_interval = @limit_interval, limit_hit_timestamp = @limit_hit_timestamp, take_value = @take_value, return_value = @return_value, wash_time = @wash_time, is_disabled = @is_disabled WHERE id = @id', {
            ['@dirty_in'] = wash.dirty_in or 0,
            ['@clean_out'] = wash.clean_out or 0,
            ['@is_washing'] = wash.is_washing and 1 or 0,
            ['@remaining_time'] = wash.remaining_time or 0,
            ['@is_washed'] = wash.is_washed and 1 or 0,
            ['@capacity'] = wash.capacity,
            ['@max_capacity'] = wash.max_capacity or config.defaults.max_capacity,
            ['@max_limit'] = wash.max_limit or config.defaults.max_limit,
            ['@limit_used'] = wash.limit_used or 0,
            ['@limit_interval'] = wash.limit_interval or config.defaults.limit_interval,
            ['@limit_hit_timestamp'] = wash.limit_hit_timestamp or nil,
            ['@take_value'] = wash.take_value or config.defaults.take_value,
            ['@return_value'] = wash.return_value or config.defaults.return_value,
            ['@wash_time'] = wash.wash_time or config.defaults.wash_time,
            ['@is_disabled'] = 0,
            ['@id'] = id
        },
        function(affected_rows)
            if affected_rows > 0 then
                print(('[boii_moneywash] Washer ID %s successfully saved on resource stop.'):format(id))
            else
                print(('[boii_moneywash] Failed to save Washer ID %s on resource stop.'):format(id))
            end
        end)
    end
end)