# BOII Development - Money Wash

**IF YOU HAVE PURCHASED THE ESCROW VERSION OF THIS RESOURCE REMOVE THE -ESCROW TAG FROM THE RESOURCE NAME**

## 🌍 Overview

Here is a slightly different approach on the typical money wash resource.

Admins can place laundering machines around the map using the built-in object placer. 
Once a washer is placed, it can be customized to allow variations in washing amounts, daily limits, processing times, and quantities across the server.

Players can interact with wash locations to exchange server-defined money or item types. For example:
- Takes item: `dirty_cash`
- Returns money balance type: `cash`
For those requiring item metadata e.g, qb-core marked billed this is accounted for.

For some added gameplay, the resource includes built-in police alerts to create roleplay scenes. 
Once a players with specified "police jobs" arrives on location they can use an item to play a memory-based pathing minigame and temporarily disable a nearby washer. 
This is configurable, allowing anyone with the item to access the minigame if preferred.

All washer data is persistently tracked, enabling monitoring of activity levels for each washer.

Enjoy. 

## 🌐 Features

- **Source-Available Resource:** Fully source-available, allowing complete customization to fit your server.
- **Placement System:** Place laundering machines on the map through an object placer, with controls over placement limits and admin permissions.
- **Multi-Currency/Item Support:** Compatible with your framework’s economy, supporting any money or item type needed.
- **Admin Tools:** Commands and UI for configuring, editing, and managing washers in-game.
- **Interactive Minigame:** A pathing minigame lets police jobs disable washers temporarily for added gameplay.
- **Customizable UI:** Modify the color palette easily in the configuration to match your server theme.
- **Data Persistence:** Washer data is stored server-side and synchronized in real-time for all clients.
- **Optimized Performance:** Caching implemented throughout to ensure better optimisation with DUI elements.

## 💹 Dependencies

- **[OxMySQL](https://github.com/overextended/oxmysql/releases/tag/v2.12.0)**
- **[BOII Utils](https://github.com/boiidevelopment/boii_utils/releases/tag/v2.0.0)**

Framework bridges of course require a supported framework core.

## 📦 Getting Started

Prior to installation make sure you have all of the dependencies listed above in your server.

1. Configuration: 

- All configuration for the washers is handled server side here: `washers/config.lua`.
- Make sure you define your admin ranks who are allowed to edit the washers.

You should be able to have enough control through the config alone however the resource is entirely source available if you want to make something a little more custom to your server, you can.

2. UI Customisation: 

- If you want to change the UI colour palette you can do do so here: `ui/css/root.css`

All of the UI elements use the root colours except one or two things changing these colours will change everything.

3. Adding The SQL:

- Included is a database file `sql.sql` you are **REQUIRED** to add this table into your database as this allows for admin editing and persistance.

4. Adding Required Items: 

- Add the following item into your inventory/framework method. 
If your framework / inventory is not covered here just use the same item name you should be fine. Any issues reach out through discord.

- QB Inventory
```lua
   moneywash_disabler = { name = 'moneywash_disabler', label = 'Moneywash Disabler', weight = 100, type = 'item', image = 'moneywash_disabler.png', unique = true, useable = true, shouldClose = true, description = 'A tablet for disabling near by money washers.' },
```
- Ox Inventory
```lua
    ['moneywash_disabler'] = {
        label = 'Moneywash Disabler',
        weight = 100,
        description = 'A tablet for disabling near by money washers.',
        close = true
    },
```

5. Adding The Resource: 

- Add `boii_moneywash` into your server resources.

6. Add The Resource To Your `server.cfg`:

- Add the following into your `server.cfg` **AFTER** any dependencies; `ensure boii_moneywash`.

Now you are good to go!
Restart your server and test out the script. 
For details on how it works read below. 

## 📦 Usage

1. Placing Your First Wash: 

- Once you have completed the installation steps *(admin ranks are important here)* you will be able to use the object placer to place washers around the city.
    - By default the command to do this is: `moneywash:place_washer`.
    - By default this is limited to 50units around the player who is placing, you can increase this if you like.
- After confirming the placement through the object placer a new washer will be spawned and synced to all connected clients.

If you want to place more than one washer just repeat the process.

2. Editing A Washer:

- To edit a washer those with admin permissions can use a command to open a simple configuration UI where they can update the stored values for each washer individually.
    - By default the command to do this is: `moneywash:admin`.
- Once a washer config is updated this will be synced to all connected clients to ensure everyone has the same washer data. *(joining players will request the updated version on entry)*

3. Washer Disable Item: 

- Included is an item for disabling the washers, by default this is restricted to "police jobs" online however you can change this in config. 
    - Using the item when in range of a washer will allow officers to complete a small memory pathing minigame to disable the washer for a period of time. 
    - Disabled washers are reset on every resource start or once the time runs out, this is not tracked persistantly after restarts. 

## 📝 Notes

- No official documentation for this currently exists, the documentation is being worked on for all resouces a.s.a.p. appreciate the patience.

## 🤝 Suggestions

Suggestions for additions to the resouce are more than welcomed. 
If you have an idea for something you think it could include leave a suggestion in the resources forum section in our discord.

## 📹 Preview

**[YouTube](https://www.youtube.com/watch?v=XlYo5l5RBO4)**

## 📩 Support

**[Discord](https://discord.gg/MUckUyS5Kq)**