CREATE TABLE IF NOT EXISTS `money_washers` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `coords` JSON NOT NULL,
    `dirty_in` INT NOT NULL DEFAULT 0, 
    `clean_out` INT NOT NULL DEFAULT 0,
    `is_washing` TINYINT(1) NOT NULL DEFAULT 0,
    `remaining_time` INT NOT NULL DEFAULT 0,
    `is_washed` TINYINT(1) NOT NULL DEFAULT 0,
    `capacity` INT NOT NULL DEFAULT 0, 
    `max_capacity` INT NOT NULL DEFAULT 10000, 
    `max_limit` INT NOT NULL DEFAULT 500000,
    `limit_used` INT NOT NULL DEFAULT 0,
    `limit_interval` INT NOT NULL DEFAULT 24,
    `limit_hit_timestamp` TIMESTAMP NULL DEFAULT NULL,
    `take_value` INT NOT NULL DEFAULT 2000,
    `return_value` INT NOT NULL DEFAULT 60,
    `wash_time` INT NOT NULL DEFAULT 24,
    `is_disabled` TINYINT(1) NOT NULL DEFAULT 0,
    `created` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
