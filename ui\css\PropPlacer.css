/* Prop Placer Main Container */
.prop_placer_container {
    position: absolute;
    left: 1vw;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2vh;
    color: var(--text_colour);
    border-radius: var(--border_radius);
    width: 25vw;
    margin: auto;
    font-family: var(--text_font_family);
}

.prop_placer_title {
    width: 100%;
    font-size: 2vh;
    font-weight: bold;
    margin-bottom: 0.5vh;
    font-family: var(--header_font_family);
    text-shadow: var(--text_shadow);
    padding: 2px;
    text-transform: uppercase;
    position: relative;
}

.prop_placer_title::after {
    position: absolute;
    bottom: 0.1vh;
    left: 0;
    content: "";
    width: 100%;
    height: 0.3vh;
    background: linear-gradient(to right, white, transparent);
    border-radius: var(--border_radius);
}

.prop_placer_controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(45%, 1fr));
    gap: 1vh;
    width: 100%;
    padding: 0.5vh;
}

.prop_placer_control {
    display: flex;
    justify-content: space-between;
    padding: 0.25vh 1vh;
    background: var(--background);
    border-radius: var(--border_radius);
    font-size: 1.5vh;
    align-items: center;
    box-shadow: var(--box_shadow);
}

.control_key {
    font-weight: bold;
    color: var(--text_colour);
    padding: 2px;
}

.control_label {
    color: var(--secondary_text_colour);
    font-style: italic;
    padding: 0 0.2vh;
}