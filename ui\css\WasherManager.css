.washer_list {
    position: absolute;
    left: 1vw;
    width: 100%;
    max-width: 15vw;
    margin: 2vh auto;
    padding: 2vh;
    border-radius: var(--border_radius);
    font-family: var(--text_font_family);
    color: var(--text_colour);
    text-shadow: var(--text_shadow);
}

.washer_list ul {
    list-style: none;
    padding: 0;
}

.washer_list li {
    margin: 1vh 0;
    padding: 0.5vh 1vh;
    border-radius: var(--border_radius);
    background: var(--background);
    color: var(--text_colour);
    box-shadow: var(--box_shadow);
    cursor: pointer;
    opacity: 0.95;
}

.washer_list li:hover {
    opacity: 1.0
}

.washer_item {
    font-size: 1.5vh;
}

.washer_config_content {
    margin-top: 1vh;
    display: flex;
    flex-direction: column;
    gap: 1vh;
}

.washer_config_header {
    text-align: center;
    font-size: 2vh;
    display: flex;
    position: relative;
    padding: 1.2vh 0;
    font-weight: bold;
    text-transform: uppercase;
}

.washer_config_header::after {
    position: absolute;
    bottom: 0.6vh;
    left: 0;
    content: "";
    width: 100%;
    height: 0.3vh;
    background: linear-gradient(to right, white, transparent);
    border-radius: var(--border_radius);
}

.washer_config_content label {
    display: block;
    font-size: 1rem;
    color: var(--text_colour);
    padding: 1px;
}

.washer_config_content input {
    width: 100%;
    padding: 0.35vh 0.75vh;
    font-size: 1.25vh;
    margin-top: 0.5vh;
    border: none;
    background: var(--tertiary_background);
    border-radius: var(--border_radius);
    box-shadow: var(--box_shadow_inset);
    color: var(--text_colour);
    font-family: var(--text_font_family);
}

.action_buttons {
    display: flex;
    justify-content: flex-end;
}

.action_buttons .save_settings {
    padding: 0.5vh 1.25vh;
    font-size: 1.25vh;
    border: none;
    cursor: pointer;
    background: var(--tertiary_background);
    color: var(--text_colour);
    box-shadow: var(--box_shadow_inset);
    transition: background 0.3s;
    border-radius: var(--border_radius);
}

.action_buttons .save_settings:hover {
    background: var(--tertiary_background);
}
