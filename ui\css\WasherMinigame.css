.tab_outer {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1vh;
    position: relative;
}

.tab_container {
    position: relative;
    height: 70vh;
    width: 60vw;
    border-radius: 40px;
    border: 0.5vh solid #b4b4b4;
    background: black;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.tab_outer_buttons {
    position: absolute;
    top: 1%;
    left: 7%;
    display: flex;
}

.volume_btn, .power_btn {
    height: 0.3vh;
    width: 3vw;
    background: #b4b4b4;
    margin: 0 0.6vh;
    border-radius: var(--border_radius) var(--border_radius) 0 0;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.5);
}

.power_btn {
    margin-right: 2.5vh;
}

.volume_btn:hover, .power_btn:hover {
    cursor: pointer;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.8);
}

.front_cam {
    position: absolute;
    top: 48%;
    left: 0.35%;
    height: 0.4vh;
    width: 0.4vh;
    background: #1f1e1e;
    border: 0.3vh solid rgba(0, 0, 0, 0.6);
    border-radius: 50%;
}

.loading_screen {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: black;
    color: white;
    font-family: Arial, sans-serif;
    font-size: 1.5rem;
}

.loading_text {
    display: flex;
    align-items: center;
}

.loading_text i {
    padding-top: 1.5vh;
    margin-left: 0.1vw;
}

.tab_screen {
    display: flex;
    flex-direction: row;
    height: 95%;
    width: 97%;
    margin: 0 auto;
    border-radius: 20px;
    background: var(--background);
    overflow: hidden;
    font-family: var(--text_font_family);
}

.left_section {
    width: 20%;
    padding: 2vh;
    color: var(--text_colour);
    border-right: 2px solid rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    border-radius: 20px 0 0 20px;
}

.left_section h3 {
    margin-bottom: 2vh;
    font-size: 2vh;
    text-align: center;
    color: var(--text_colour);
    font-family: var(--header_font_family);
}

.washer_grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(15ch, 1fr));
    gap: 1.5vh;
    width: 100%;
    padding: 1vh;
}

.washer_card {
    background: var(--secondary_background);
    border-radius: var(--border-radius);
    color: #dcdcdc;
    padding: 1vh;
    box-shadow: var(--box_shadow_inset);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.washer_card h4 {
    font-size: 1vh;
}

.washer_card p {
    margin: 0;
    font-size: 1.2vh;
}

.washer_card:hover {
    transform: translateY(-3px);
    background: var(--tertiary_background);
}

.washer_card.disabled {
    background: var(--tertiary_background);
    color: var(--tertiary_text_colour);
    cursor: not-allowed;
    box-shadow: none;
}

.washer_status {
    color: green;
    font-weight: bold;
}

.washer_card.disabled .washer_status {
    color: var(--secondary_accent_colour);
}

.washer_card.disabled:hover {
    transform: none;
    box-shadow: none;
}

.right_section {
    width: 80%;
    padding: 2vh;
    color: var(--text_colour);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.minigame_container {
    width: 95%;
    height: 94%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    background: var(--secondary_background);
    box-shadow: var(--box_shadow_inset);
    padding: 2vh;
}

.game_container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.minigame_grid {
    display: grid;
    grid-template-columns: repeat(5, auto);
    gap: 0.5vh;
    margin-top: 2vh;
    padding: 1vh;
}

.cell {
    width: 4.5vw;
    height: 4.5vw;
    background: var(--background);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--box_shadow_inset);
    cursor: pointer;
    transition: background 0.2s ease;
    font-size:2.5vh;
}

.cell.start {
    background: green;
}

.cell.end {
    background: var(--secondary_accent_colour);
}

.cell.preview {
    background: var(--tertiary_accent_colour);
}

.cell.active {
    background: var(--accent_colour);
}

.cell.disabled {
    pointer-events: none;
}

.minigame_details {
    position: absolute;
    top: 1vh;
    display: flex;
    width: 100%;
    justify-content: space-around;
    font-family: var(--header_font_family);
    font-size: 1.5vh;
}

.message {
    position: absolute;
    top: 4vh;
    font-size: 1.5vh;
    font-weight: bold;
    text-align: center;
}

.start_button {
    margin-top: 2vh;
    padding: 1vh 2vh;
    font-size: 2vh;
    cursor: pointer;
    background: var(--background);
    color: var(--text_colour);
    border: none;
    border-radius: var(--border_radius);
    transition: background 0.3s ease;
    text-transform: capitalise;
    box-shadow: var(--box_shadow_inset);
}

.start_button:hover {
    background: green;
}