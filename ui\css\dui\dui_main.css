.interact_ui {
    height: fit-content;
    width: fit-content;
    min-width: 12vw;
    padding: 2vh;
    color: var(--text_colour);
    text-shadow: var(--text_shadow);
    font-family: var(--text_font_family);
}

.interact_ui h3 {
    font-size: 1.5vh;
    font-weight: normal;
    padding: 0 2vh;
    margin-bottom: 0.2vh;
}

.interact_header {
    width: 100%;
    font-size: 2.5vh;
    font-family: var(--header_font_family);
    text-transform: uppercase;
    position: relative;
    padding: 0 1vh;
}

.interact_header::after {
    position: absolute;
    bottom: 0.8vh;
    left: 0;
    content: "";
    width: 100%;
    height: 0.3vh;
    background: linear-gradient(to right, white, transparent);
    border-radius: var(--border_radius);
}

.header_content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 1.5vh 0.7vh;
}

.header_content i {
    font-size: 2vh;
    margin-top: 0.5vh;
    margin-right: 1vh;
}

.keys_container {
    padding: 0 1.05vh;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25vh;
}

.interaction_key {
    display: flex;
    align-items: center;
    color: var(--text_colour);
    font-family: var(--header_font_family);
    text-shadow: var(--text_shadow);
    font-size: 1.6vh;
    padding: 0.25vh 0.5vh;
}

.key_label {
    padding: 0.1vh;
    color: var(--text_colour);
}

.key {
    background: var(--secondary_background);
    height: 1.8vh;
    width: 1.8vh;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: var(--border_radius);
    color: var(--text_colour);
    font-size: 1.5vh;
    text-transform: uppercase;
    margin-right: 0.75vh;
    box-shadow: var(--box_shadow);
}

.interact_progress_container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 95%;
    padding: 0.5vh 0.9vw;
    text-align: center;
    position: relative;
    margin: 0 -0.15vh;
}

.interact_progress_header {
    position: absolute;
    display: flex;
    width: 96%;
    height: 100%;
    align-items: center;
    justify-content: flex-end;
    font-size: 1.2vh;
    padding-left: 0.5vh;
}

.interact_progress_bar {
    width: 98%;
    height: 1.8vh;
    background: var(--tertiary_background);
    border-radius: var(--border_radius);
    box-shadow: var(--box_shadow);
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.interact_progress_bar_fill {
    height: 100%;
    background: linear-gradient(to right, var(--secondary_background), var(--background));
    transition: width 0.3s ease;
}

@keyframes flash {
	0% { opacity: 1; } 
	50% { opacity: .1; } 
	100% { opacity: 1; }
}