* {
    margin: 0;
    padding: 0;
    overflow: hidden;
}

body {
    //background: grey;
}

.hidden {
    display: none !important;
}

textarea:focus,
input:focus,
button:focus {
    outline: none;
}

i {
    padding: 2px; 
}

p {
    padding: 2px;
}

#main_container {
    height: 100vh;
    width: 100vw;
    margin: 0;
    padding: 0;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bg_layer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(to right, rgba(31, 30, 30, 0.6) 0%, rgba(31, 30, 30, 0.3) 15%, rgba(31, 30, 30, 0.1) 25%),
                linear-gradient(to left, rgba(31, 30, 30, 0.6) 0%, rgba(31, 30, 30, 0.3) 15%, rgba(31, 30, 30, 0.1) 25%);
    z-index: 0;
    display: none;
}

::-webkit-scrollbar {
    width: 0;
    height: 0;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}