class WasherDUI {
    constructor(options) {
        this.options = options;
        this.currency = options.currency || '$';
        this.capacity = options.capacity || 0;
        this.max_capacity = options.max_capacity || 10000;
        this.limit = options.limit || 500000;
        this.limit_used = options.limit_used || 0;
        this.limit_interval = options.limit_interval || 24;
        this.is_washing = options.is_washing || false;
        this.remaining_time = options.remaining_time || 0;
        this.keys = options.keys || [];
        this.icon = options.icon || '';
        this.header = options.header || 'Money Wash';
        this.timer_interval = null;
        this.build();
    }

    build() {
        const key_hints = this.keys.map(key_obj => {
            return `<div class="interaction_key"><span class="key"><p>${key_obj.key}</p></span> <span class="key_label">${key_obj.label}</span></div>`;
        }).join('');
        const capacity_progress = `
            <div class="interact_progress_container">
                <div class="interact_progress_bar">
                    <div class="interact_progress_bar_fill" style="width: ${(this.capacity / this.max_capacity) * 100}%; transition: width 0.5s;">
                        <div class="interact_progress_header">${this.currency}${this.capacity} / ${this.currency}${this.max_capacity}</div>
                    </div>
                </div>
            </div>
        `;
        const limit_progress = `
            <div class="interact_progress_container">
                <div class="interact_progress_bar">
                    <div class="interact_progress_bar_fill" style="width: ${(this.limit_used / this.limit) * 100}%; transition: width 0.5s;">
                        <div class="interact_progress_header">${this.currency}${this.limit_used} / ${this.currency}${this.limit}</div>
                    </div>
                </div>
            </div>
        `;
        const washing_status = this.limit_used >= this.limit ? 'Limit Reached' : this.is_washing ? `Remaining Time: ${this.format_time(this.remaining_time)}` : 'Inactive';
        const washing_progress = `
            <div class="interact_progress_container">
                <div class="interact_progress_bar">
                    <div class="interact_progress_bar_fill washing_fill" style="width: ${this.is_washing ? `${(this.remaining_time / (this.limit_interval * 60 * 60)) * 100}%` : '0%'}; transition: width 1s;">
                        <div class="interact_progress_header washing_header">${washing_status}</div>
                    </div>
                </div>
            </div>
        `;
        const content = `
            <div class="interact_ui">
                <div class="interact_header">
                    <span class="header_content"><i class="${this.icon}"></i> ${this.header}</span>
                </div>
                <h3>Capacity:</h3>
                ${capacity_progress}
                <h3>Daily Limit:</h3>
                ${limit_progress}
                <h3>Washing Status:</h3>
                ${washing_progress}
                <div class="keys_container">
                    ${key_hints}
                </div>
            </div>
        `;
        $('#main_container').html(content);
        if (this.is_washing && this.remaining_time > 0) {
            this.start_timer();
        }
    }

    format_time(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remaining_seconds = seconds % 60;
        return `${minutes}m ${remaining_seconds}s`;
    }

    start_timer() {
        const header = $('.washing_header');
        const fill = $('.washing_fill');
        if (this.timer_interval) clearInterval(this.timer_interval);
        this.timer_interval = setInterval(() => {
            if (this.remaining_time > 0) {
                this.remaining_time--;
                if (header.length) {
                    header.text(`Remaining Time: ${this.format_time(this.remaining_time)}`);
                }
                if (fill.length) {
                    const progress_width = `${(this.remaining_time / (this.limit_interval * 60 * 60)) * 100}%`;
                    fill.css('width', progress_width);
                }
            } else {
                clearInterval(this.timer_interval);
                this.timer_interval = null;
                this.is_washing = false;
                if (header.length) {
                    header.text('Inactive');
                }
                if (fill.length) {
                    fill.css('width', '0%');
                }
            }
        }, 1000);
    }

    close() {
        $('#main_container').empty();
    }
}

/*
const table_ui = new WasherDUI({
    header: 'Money Wash',
    icon: 'fa-solid fa-user-ninja',
    capacity: 0,
    max_capacity: 10000,
    limit: 500000,
    limit_used: 0,
    limit_interval: 24,
    is_washing: true,
    remaining_time: 120,
    keys: [
        { key: 'E', label: 'Add Money' },
        { key: 'R', label: 'Remove Money' },
        { key: 'H', label: 'Start Washing' }
    ]
});
*/
