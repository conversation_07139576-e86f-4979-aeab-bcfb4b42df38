class WasherManager {
    constructor(washers) {
        this.washers = washers;
        this.bind_exit_event();
    }

    bind_exit_event() {
        $(document).ready(() => {
            $(document).keyup((e) => this.handle_exit(e));
        });
    }

    handle_exit(e) {
        if (e.key === 'Escape') {
            this.close();
        }
    }

    close() {
        WASHER_CONFIG = null;
        $("#main_container").empty();
        $.post(`https://${GetParentResourceName()}/remove_ui_focus`, {});
    }

    display_washers() {
        let washer_list = '<div class="washer_list"><div class="washer_config_header">Washers</div><ul>';
        this.washers.forEach((washer, index) => {
            washer_list += `
                <li data-index="${index}">
                    <div class="washer_item">Washer: ${washer.id}</div>
                    <div class="washer_config_content hidden">
                        <label>Take Value: <input type="number" class="take_value" value="${washer.take_value}"></label>
                        <label>Return Value (%): <input type="number" class="return_value" value="${washer.return_value}"></label>
                        <label>Max Capacity: <input type="number" class="max_capacity" value="${washer.max_capacity}"></label>
                        <label>Max Limit: <input type="number" class="max_limit" value="${washer.max_limit}"></label>
                        <label>Limit Interval (hours): <input type="number" class="limit_interval" value="${washer.limit_interval}"></label>
                        <label>Wash Time (seconds): <input type="number" class="wash_time" value="${washer.wash_time}"></label>
                        <div class="action_buttons">
                            <button type="button" class="save_settings">Save</button>
                        </div>
                    </div>
                </li>`;
        });
        washer_list += '</ul></div>';
        $('#main_container').html(washer_list);
        this.attach_events();
    }

    attach_events() {
        $('.washer_list li').on('click', function () {
            const content = $(this).find('.washer_config_content');
            $('.washer_config_content').not(content).addClass('hidden');
            content.toggleClass('hidden');
        });

        $('.washer_config_content input').on('click', function (event) {
            event.stopPropagation();
        });

        $('.save_settings').on('click', (event) => {
            event.stopPropagation();
            const li = $(event.currentTarget).closest('li');
            const index = li.data('index');
            const washer = this.washers[index];
            washer.take_value = parseInt(li.find('.take_value').val());
            washer.return_value = parseInt(li.find('.return_value').val());
            washer.max_capacity = parseInt(li.find('.max_capacity').val());
            washer.max_limit = parseInt(li.find('.max_limit').val());
            washer.limit_interval = parseInt(li.find('.limit_interval').val());
            washer.wash_time = parseInt(li.find('.wash_time').val());
            $.post(`https://${GetParentResourceName()}/update_washer`, JSON.stringify(washer));
        });
    }
    
}

/*
const test_washers = [
    { id: 1, take_value: 2000, return_value: 60, max_capacity: 10000, max_limit: 500000, limit_interval: 24, wash_time: 10 },
    { id: 2, take_value: 3000, return_value: 75, max_capacity: 15000, max_limit: 600000, limit_interval: 12, wash_time: 20 },
];

// Initialize Washer Manager and show the washer list
const test_washer_admin = new WasherManager(test_washers);
test_washer_admin.display_washers();
*/