class WasherMinigame {
    constructor(washers) {
        this.washers = washers || [];
        this.selected_path = [];
        this.active_path = [];
        this.grid_size = 5;
        this.time_left = 15;
        this.attempts_left = 3;
        this.timer = null;
        this.preview_time = 3000;
        this.game_active = false;
        this.current_washer_id = null;
        this.paths = [
            [[0, 0], [1, 0], [2, 0], [2, 1], [3, 1], [4, 1], [4, 2], [4, 3], [3, 3], [2, 3], [1, 3], [0, 3], [0, 4]],
            [[0, 0], [1, 0], [2, 0], [3, 0], [4, 0], [4, 1], [4, 2], [3, 2], [2, 2], [1, 2], [0, 2], [0, 3], [0, 4]],
            [[0, 0], [0, 1], [1, 1], [2, 1], [2, 2], [3, 2], [4, 2], [4, 3], [3, 3], [2, 3], [1, 3], [0, 3], [0, 4]]
        ];
        this.build_tablet();
        this.start_loading_sequence();
        $(document).ready(() => {
            $(document).keyup((e) => this.handle_exit(e));
            $(document).on("click", ".washer_card", (e) => this.handle_washer_click(e));
        });
    }

    handle_exit(e) {
        if (e.key === "Escape") {
            this.close();
        }
    }

    close() {
        $('#main_container').empty();
        $.post(`https://${GetParentResourceName()}/remove_ui_focus`, JSON.stringify({}));
        WASHER_MINIGAME = null;
    }

    build_tablet() {
        const content = `
            <div class="tab_outer">
                <div class="tab_outer_buttons">
                    <div class="power_btn"></div>
                    <div class="volume_btn"></div>
                    <div class="volume_btn"></div>
                </div>
                <div class="tab_container">
                    <div class="front_cam"></div>
                    <div class="loading_screen">
                        <div class="loading_text">Connecting<i class="fa-solid fa-ellipsis fa-fade"></i></div>
                    </div>
                </div>
            </div>    
        `;
        $('#main_container').html(content);
    }

    start_loading_sequence() {
        setTimeout(() => {
            this.show_main_screen();
        }, 3000);
    }

    show_main_screen() {
        $(".tab_container").empty();
        $('.loading_screen').remove();
        const content = `
            <div class="tab_screen">
                <div class="left_section">
                    <h3>Nearby Washers</h3>
                    <div class="washer_grid">
                        ${this.washers.map(washer => `
                            <div class="washer_card ${washer.is_disabled ? 'disabled' : ''}" data-id="${washer.id}">
                                <h4>ID: ${washer.id}</h4>
                                <p class="washer_status">${washer.is_disabled ? 'Disabled' : 'Active'}</p>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="right_section">
                    <div class="minigame_container">
                        <p>Select a washer to start.</p>
                    </div>
                </div>
            </div>
        `;
        $(".tab_container").html(content);
    }
    

    handle_washer_click(e) {
        const washer_ele = $(e.currentTarget);
        if (washer_ele.hasClass("disabled")) {
            return;
        }
        const washer_id = washer_ele.data("id");
        this.current_washer_id = washer_id;
        this.start_minigame(washer_id);
    }
    
    start_minigame(washer_id) {
        this.select_random_path();
        this.attempts_left = 3;
        this.game_active = false;
        this.update_minigame_ui(washer_id);
    }

    update_minigame_ui(washer_id) {
        const content = `
            <div class="game_container">
                <div class="minigame_details">
                    <div class="washer_id">Washer ID: <span id="washer_id">${washer_id}</span></div>
                    <div class="attempts">Remaining Attempts: <span id="attempts">${this.attempts_left}</span></div>
                    <div class="timer">Remaining Time: <span id="timer">${this.time_left}</span>s</div>
                </div>
                <div class="message" id="message"></div>
                <div class="minigame_grid" id="minigame_grid"></div>
                <button class="start_button" id="start_button">Start Attempt</button>
            </div>
        `;
        $(".minigame_container").html(content);
        $("#start_button").click(() => this.start_attempt());
        this.create_grid();
    }

    select_random_path() {
        this.selected_path = this.paths[Math.floor(Math.random() * this.paths.length)];
    }

    create_grid() {
        $("#minigame_grid").empty();
        for (let row = 0; row < this.grid_size; row++) {
            for (let col = 0; col < this.grid_size; col++) {
                const cell = $("<div>").addClass("cell disabled").attr({ "data-row": row, "data-col": col });
                if (row === 0 && col === 0) {
                    cell.addClass("start").html('<i class="fas fa-bolt"></i>');
                } else if (row === 0 && col === 4) {
                    cell.addClass("end").html('<i class="fas fa-bahai"></i>');
                }
                cell.click(() => this.activate_cell(cell));
                $("#minigame_grid").append(cell);
            }
        }
    }

    start_attempt() {
        if (this.game_active) return;
        this.game_active = true;
        this.active_path = [];
        $("#message").text("");
        $("#timer").text(this.time_left);
        this.create_grid();
        this.preview_path();
    }

    preview_path() {
        this.selected_path.forEach(([r, c]) => {
            $(`.cell[data-row='${r}'][data-col='${c}']`).addClass("preview");
        });

        setTimeout(() => {
            $(".preview").removeClass("preview");
            this.enable_cells();
            this.start_timer();
        }, this.preview_time);
    }

    enable_cells() {
        $(".cell").removeClass("disabled");
    }

    activate_cell(cell) {
        if (!this.game_active || cell.hasClass("disabled")) return;
        const row = parseInt(cell.attr("data-row"));
        const col = parseInt(cell.attr("data-col"));
        const last_step = this.active_path.length > 0 ? this.active_path[this.active_path.length - 1] : null;
        if (cell.hasClass("active")) return;
        if (last_step) {
            const [last_row, last_col] = last_step;
            if (!this.is_adjacent(last_row, last_col, row, col)) return;
        } else if (row !== 0 || col !== 0) {
            this.fail_attempt("You failed! Start from ⚡!");
            return;
        }
        cell.addClass("active");
        this.active_path.push([row, col]);
    
        if (row === 0 && col === 4) {
            if (this.paths_match()) {
                $("#message").text("Machine Disabled Successfully!");
                clearInterval(this.timer);
                this.game_active = false;
                const washer_ele = $(`.washer_card[data-id='${this.current_washer_id}']`);
                washer_ele.addClass("disabled");
                washer_ele.find(".washer_status").text("Disabled");
                this.washers = this.washers.map(washer =>
                    washer.id === this.current_washer_id ? { ...washer, is_disabled: true } : washer
                );
                setTimeout(() => this.show_main_screen(), 2000);
                $.post(`https://${GetParentResourceName()}/disable_washer`, JSON.stringify({ washer: this.current_washer_id }));
            } else {
                this.fail_attempt("You Failed! Incorrect Path!");
            }
        }
    }
    
    is_adjacent(r1, c1, r2, c2) {
        return (Math.abs(r1 - r2) + Math.abs(c1 - c2)) === 1;
    }

    paths_match() {
        return JSON.stringify(this.active_path) === JSON.stringify(this.selected_path);
    }

    fail_attempt(msg) {
        this.attempts_left--;
        $("#attempts").text(this.attempts_left);
        $(".cell").addClass("disabled");
        clearInterval(this.timer);
        if (this.attempts_left === 0) {
            $("#message").text("You Failed! Try Again!");
            this.game_active = false;
            setTimeout(() => this.show_main_screen(), 2000); 
            return;
        }
        $("#message").text(msg);
        setTimeout(() => {
            this.active_path = [];
            this.game_active = false;
            $("#message").text("");
        }, 3000);
    }

    start_timer() {
        clearInterval(this.timer);
        this.time_left = 15;
        this.timer = setInterval(() => {
            if (this.time_left <= 0) {
                clearInterval(this.timer);
                this.fail_attempt("You Failed! Ran Out Of Time! ⏳");
                return;
            }
            this.time_left--;
            $("#timer").text(this.time_left);
        }, 1000);
    }
}

//const test_tab = new WasherMinigame([{ id: 1, is_disabled: false }, { id: 2, is_disabled: true }, { id: 3, is_disabled: false }]);