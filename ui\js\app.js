let PROP_PLACER = null;
let WASHER_DUI = null;
let WASHER_CONFIG = null;
let WASHER_MINIGAME = null;

const handlers = {
    open_prop_placer: (data) => {
        if (!PROP_PLACER) {
            PROP_PLACER = new PropPlacer(data);
            PROP_PLACER.build_control_display(data.controls); // Build the UI
        }
    },
    close_prop_placer: () => {
        if (PROP_PLACER) {
            PROP_PLACER = null; // Destroy instance
            $('.prop_placer_container').remove();
        }
    },
    show_washer_dui: (data) => {
        if (WASHER_DUI) {
            WASHER_DUI.close();
        }
        WASHER_DUI = new WasherDUI(data.options);
    },
    close_washer_dui: () => {
        if (WASHER_DUI) {
            WASHER_DUI.close();
        }
    },
    open_washer_config: (data) => {
        if (WASHER_CONFIG) {
            WASHER_CONFIG.close();
        }
        WASHER_CONFIG = new WasherManager(data.washers);
        WASHER_CONFIG.display_washers();
    },
    close_washer_config: () => {
        if (WASHER_CONFIG) {
            WASHER_CONFIG.close();
        }
    },
    open_washer_minigame: (data) => {
        if (WASHER_MINIGAME) {
            WASHER_MINIGAME.close();
        }
        WASHER_MINIGAME = new WasherMinigame(data.washers);
    }
};

window.addEventListener('message', function (event) {
    const data = event.data;
    const handler = handlers[data.action];
    if (handler) {
        handler(data);
    }
});