--- @section Constants

local DUI_RANGE <const> = 2.0
local DUI_RANGE_SQUARED <const> = DUI_RANGE * DUI_RANGE
local KEY_LIST <const> = KEYS.get_keys()

--- @section Tables

local dui_locations = {}

--- @section Functions

--- Creates a DUI object for a specified location
--- @param location_id string: The unique ID of the location
--- @return table: A table containing the DUI object and texture data
local function create_dui(location_id)
    local txd_name, txt_name = location_id, location_id
    local dui_url = 'https://cfx-nui-' .. RESOURCE_NAME .. '/ui/index.html'
    local screen_width, screen_height = GetActiveScreenResolution()
    local dui_object = CreateDui(dui_url, screen_width, screen_height)
    local txd = CreateRuntimeTxd(txd_name)
    CreateRuntimeTextureFromDuiHandle(txd, txt_name, GetDuiHandle(dui_object))
    return { dui_object = dui_object, txd_name = txd_name, txt_name = txt_name }
end

--- Adds a new zone to the DUI system
--- @param options table: A table containing zone options
function add_zone(options)
    if not options.id or not options.coords or not options.header then
        print('Error: Missing required fields for zone.')
        return
    end
    for _, key_data in ipairs(options.keys or {}) do
        key_data.key_control = KEY_LIST[string.lower(key_data.key)]
        if not key_data.key_control then
            print(('[Warning] Invalid key "%s" specified for zone "%s".'):format(key_data.key, options.id))
        end
    end
    dui_locations[options.id] = {
        id = options.id,
        model = options.model,
        coords = options.coords,
        header = options.header,
        icon = options.icon or '',
        capacity = options.capacity,
        max_capacity = options.max_capacity,
        limit = options.limit,
        limit_used = options.limit_used,
        limit_interval = options.limit_interval,
        is_washing = options.is_washing,
        remaining_time = options.remaining_time,
        keys = options.keys,
        outline = options.outline,
        dui_object = create_dui(options.id),
        in_proximity = false
    }
end

--- Handles key press interactions for a location
--- @param location table: The location table
local function handle_key_presses(location)
    for _, key_data in ipairs(location.keys) do
        if IsControlJustReleased(0, key_data.key_control) then
            if key_data.action_type == 'client' then
                TriggerEvent(key_data.action, location.id)
            elseif key_data.action_type == 'server' then
                TriggerServerEvent(key_data.action, location.id)
            end
        end
    end
end

--- Renders a single zone's DUI
--- @param location table: The location table
--- @param player_coords vector3: The player's coordinates
local function render_dui(location, player_coords)
    local dui = location.dui_object
    if not dui then return end
    SetDrawOrigin(location.coords.x, location.coords.y, player_coords.z + 0.5)
    if HasStreamedTextureDictLoaded(dui.txd_name) then
        DrawInteractiveSprite(dui.txd_name, dui.txt_name, 0, 0, 1.0, 1.0, 0.0, 255, 255, 255, 255)
    end
    local new_message = json.encode({
        action = 'show_washer_dui',
        options = {
            header = location.header,
            model = location.model,
            icon = location.icon,
            capacity = location.capacity,
            max_capacity = location.max_capacity,
            limit = location.limit,
            limit_used = location.limit_used,
            limit_interval = location.limit_interval,
            is_washing = location.is_washing,
            remaining_time = location.remaining_time,
            keys = location.keys,
            outline = location.outline
        }
    })
    if location.last_message ~= new_message then
        SendDuiMessage(dui.dui_object, new_message)
        location.last_message = new_message
    end
end

--- Syncs dui updates from server with client UI.
--- @param id number: Washer ID.
--- @param updated_data table: The new washer data.
RegisterNetEvent('boii_moneywash:cl:sync_dui_data', function(id, updated_data)
    if not dui_locations[id] then
        print(('[Error] Zone with ID %s not found during sync.'):format(id))
        return
    end
    for key, value in pairs(updated_data) do
        dui_locations[id][key] = value
    end
end)

--- Toggles an entitys outline visibility
--- @param entity number: The entity ID
--- @param state boolean: Whether to enable or disable the outline
local function toggle_outline(entity, state)
    if not entity then return end
    SetEntityDrawOutline(entity, state)
    if state then
        SetEntityDrawOutlineColor(255, 255, 255, 255)
        SetEntityDrawOutlineShader(1)
    end
end

--- Main Proximity and Rendering Thread
CreateThread(function()
    while true do
        Wait(0)
        if not is_placing then
            local player_coords = GetEntityCoords(PlayerPedId())
            local has_rendered = false
            for _, location in pairs(dui_locations) do
                if location.is_disabled then break end
                local dx = player_coords.x - location.coords.x
                local dy = player_coords.y - location.coords.y
                local dz = player_coords.z - location.coords.z
                local distance_squared = dx * dx + dy * dy + dz * dz
                if distance_squared <= DUI_RANGE_SQUARED then
                    if not location.in_proximity then
                        location.in_proximity = true
                    end
                    render_dui(location, player_coords)
                    handle_key_presses(location)
                    has_rendered = true
                    if location.outline then toggle_outline(location.model, true) end
                elseif location.in_proximity then
                    location.in_proximity = false
                    if location.outline then toggle_outline(location.model, false) end
                end
            end
            if has_rendered then ClearDrawOrigin() end
        else
            Wait(1000)
        end
    end
end)

