--- @section Functions

--- Opens the washer disabler tablet.
--- @param washers table: Table of nearby washers.
local function open_tablet(data)
    if disable_table_open then print('tablet is already open') return end
    SetNuiFocus(true, true)
    SendNUIMessage({ action = 'open_washer_minigame', washers = data})
end

--- @section NUI Callbacks

--- Callback for disabling a tablet on game success.
--- @param data table: UI post data.
RegisterNUICallback('disable_washer', function(data)
    if not data then print('disable data missing') return end
    TriggerServerEvent('boii_moneywash:sv:disable_washer', data.washer)
end)

--- @section Events

--- Triggers the open tablet function.
RegisterNetEvent('boii_moneywash:cl:disable_tablet', function(data)
    SetNuiFocus(true, true)
    if not data then print('washers data missing') return end
    open_tablet(data)
end)