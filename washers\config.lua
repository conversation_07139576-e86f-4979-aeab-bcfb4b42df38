config = {}

--- 定期保存计时器
config.periodic_save_timer = 1 -- 定期保存洗钱机的时间间隔（分钟），资源停止时也会保存

--- 管理员权限等级
--- 这里的等级允许放置和修改现有的洗钱机
--- 这些是 `boii_utils` 管理员等级；默认等级：('member', 'mod', 'admin', 'dev', 'owner')
--- 如果不希望所有人都能使用命令，请移除 'member' <--- 强烈推荐
config.admin_ranks = { 'member', 'mod', 'admin', 'dev', 'owner' }

--- 默认洗钱设置
config.defaults = {
    take_value = 2000, -- 每次增加的金额
    return_value = 60, -- 新洗钱机的返还价值 60%
    max_capacity = 10000, -- 每次交互可洗钱的最大金额 $10000
    max_limit = 500000, -- 每个时间间隔内可洗钱的限制，默认 = 24小时内50万
    limit_interval = 24, -- 重置限制的小时数
    wash_time = 120, -- 洗钱时间（秒）
}

--- 洗钱机的DUI操作
config.actions = {
    {
        key = 'G', -- 交互按键
        label = '添加金钱', -- 按键交互的显示标签
        action_type = 'server',
        action = 'boii_moneywash:sv:add_money'
    },
    {
        key = 'H', -- 交互按键
        label = '取出金钱', -- 按键交互的显示标签
        action_type = 'server',
        action = 'boii_moneywash:sv:remove_money'
    },
    {
        key = 'F', -- 交互按键
        label = '开始洗钱', -- 按键交互的显示标签
        action_type = 'server',
        action = 'boii_moneywash:sv:start_washing'
    }
}

--- 道具设置
config.prop = {
    model = 'bkr_prop_prtmachine_dryer_spin', -- 洗钱机生成的道具模型
    header = '洗钱机', -- DUI标题
    icon = 'fa-solid fa-user-ninja', -- DUI标题图标
    outline = true
}

--- 警察设置
config.police = {
    alert_chance = 100, -- 洗钱时警报警察的几率
    required_to_wash = 2, -- 洗钱所需的在线警察工作数量，设置为0表示无警察时也可洗钱
    disable_duration = 1, -- 警察工作可以禁用洗钱机的时间（分钟）
    disable_range = 10, -- 警察玩家可以禁用附近洗钱机的距离
    on_duty_only = true, -- 如果为true，只计算在职工作
    jobs = { 'police', 'fib' } -- 被归类为"警察"的工作名称
}

--- 洗钱机取钱设置
config.takes = {
    type = 'money', -- 选项：'money' 或 'item'
    source = { -- 取钱来源
        id = 'cash', -- 账户ID或物品ID，用于取钱
        metadata = { -- 如果需要元数据，确保包含 `.` 分隔符，例如 qb markedbills 使用 `info.worth`
           -- 'info.worth' 如果使用 `markedbills` 请取消注释
        }
    }
}

--- 洗钱机返钱设置
config.returns = {
    type = 'money', -- 选项：'money' 或 'item'
    source = {
        id = 'bank', -- 账户ID或物品ID，用于返钱
        metadata = {} -- 如果需要元数据，例如 qb marked bills
    }
}