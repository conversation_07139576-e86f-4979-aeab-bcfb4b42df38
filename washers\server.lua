--- @section Internal Functions

--- 获取玩家的所有标识符
--- @param source number: 玩家源ID
--- @return table: 包含所有标识符的表
local function get_player_identifiers(source)
    local identifiers = {}
    for _, identifier in pairs(GetPlayerIdentifiers(source)) do
        table.insert(identifiers, identifier)
    end
    return identifiers
end

--- 检查玩家是否有管理员权限
--- @param source number: 玩家源ID
--- @return boolean: 如果有权限返回true，否则返回false
local function has_permission(source)
    local admin_identifiers = config.admin_identifiers

    -- 如果设置为允许所有人
    if admin_identifiers[1] == 'all' then
        return true
    end

    -- 获取玩家的所有标识符
    local player_identifiers = get_player_identifiers(source)

    -- 检查玩家的标识符是否在管理员列表中
    for _, admin_id in pairs(admin_identifiers) do
        for _, player_id in pairs(player_identifiers) do
            if admin_id == player_id then
                return true
            end
        end
    end

    -- 没有权限，发送错误消息
    NOTIFICATIONS.send(source, {
        type = 'error',
        header = '洗钱机',
        message = '您没有权限执行此操作。',
        duration = 3500
    })
    return false
end

--- Extracts a nested value from an inventory item using a dot-separated key path.
--- Example: extract_metadata_value(item, "info.worth") -> returns item.info.worth
local function extract_metadata_value(item, key_path)
    if not item or not key_path then return nil end
    local value = item
    for key in key_path:gmatch("[^.]+") do
        if type(value) ~= "table" or value[key] == nil then
            return nil
        end
        value = value[key]
    end
    return value
end

--- Processes marked bills: calculates total worth, removes all, and returns remaining.
--- @param source number: The player ID.
--- @param amount number: The amount to deduct.
--- @param metadata_key string: The metadata key path (e.g., "info.worth").
--- @return boolean success, number remaining_worth
local function process_marked_bills(source, amount, metadata_key)
    local inventory = CORE.get_inventory(source)
    if not inventory then 
        NOTIFICATIONS.send(source, { type = 'error', header = '洗钱机', message = '未找到背包。', duration = 5000 })
        return false, 0
    end
    local total_worth, total_amount = 0, 0
    for _, item in pairs(inventory) do
        if item.name == 'markedbills' then
            local worth = extract_metadata_value(item, metadata_key) or 0
            total_worth = total_worth + worth
            local quantity = item.amount or item.quantity or item.count or 1
            total_amount = total_amount + quantity
        end
    end
    if total_worth == 0 then 
        NOTIFICATIONS.send(source, { type = 'error', header = '洗钱机', message = '您没有标记钞票。', duration = 5000 })
        return false, 0
    end
    if total_worth < amount then
        NOTIFICATIONS.send(source, { type = 'error', header = '洗钱机', message = '标记钞票价值不足。', duration = 5000 })
        return false, 0
    end
    CORE.remove_item(source, 'markedbills', total_amount)
    return true, total_worth - amount
end

--- @section Events

--- Adds money into a washer.
RegisterServerEvent('boii_moneywash:sv:add_money', function(wash_id)
    local src = source
    local takes, wash, amount = config.takes, placed_objects[wash_id], config.defaults.take_value
    if not wash then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '无效的洗钱机ID。', duration = 5000 }) end
    if wash.is_washing then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '此洗钱机正在运行中，无法添加金钱。', duration = 5000 }) end
    if wash.is_washed and wash.capacity > 0 then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '此洗钱机内有干净的钱，请先清空再添加更多金钱。', duration = 5000 }) end
    if wash.capacity + amount > config.defaults.max_capacity then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '此洗钱机容量已超限。', duration = 5000 }) end
    if CORE.player_has_job(src, config.police.jobs, config.police.on_duty_only) then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '您的职业不允许执行此操作。', duration = 3500 }) end
    if takes.type == 'money' then
        local player_balance = CORE.get_balance_by_type(src, takes.source.id)
        if not player_balance or player_balance == false then
            print(('[boii_moneywash] 无法获取玩家 %s 的余额'):format(src))
            return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '无法获取账户余额，请稍后再试。', duration = 5000 })
        end
        if player_balance < amount then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '资金不足。', duration = 5000 }) end
        CORE.remove_balance(src, takes.source.id, amount)
    elseif takes.type == 'item' then
        if takes.source.id == 'markedbills' then
            local success, remaining_worth = process_marked_bills(src, amount, takes.source.metadata[1])
            if not success then return end
            if remaining_worth > 0 then CORE.add_item(src, 'markedbills', 1, { worth = remaining_worth }) end
        else
            if not CORE.has_item(src, takes.source.id, amount) then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '您没有足够的物品。', duration = 5000 }) end
            CORE.remove_item(src, takes.source.id, amount)
        end
    else
        return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '无效的来源类型。', duration = 5000 })
    end
    wash.capacity = wash.capacity + amount
    placed_objects[wash_id] = wash
    TriggerClientEvent('boii_moneywash:cl:sync_dui_data', -1, wash_id, { capacity = wash.capacity })
    NOTIFICATIONS.send(src, { type = 'success', header = '洗钱机', message = ('已向洗钱机添加 $%d 金钱。'):format(amount), duration = 5000 })
end)

--- Removes money from a washer.
RegisterServerEvent('boii_moneywash:sv:remove_money', function(wash_id)
    local src, wash, takes, returns, amount = source, placed_objects[wash_id], config.takes, config.returns, config.defaults.take_value
    if not wash then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '无效的洗钱机ID。', duration = 5000 }) end
    if wash.is_washing then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '此洗钱机正在运行中，无法取出金钱。', duration = 5000 }) end
    if wash.capacity < amount then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '洗钱机内金钱不足。', duration = 5000 }) end
    if CORE.player_has_job(src, config.police.jobs, config.police.on_duty_only) then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '您的职业不允许执行此操作。', duration = 3500 }) end
    local return_type = wash.is_washed and returns or takes
    if return_type.type == 'money' then
        CORE.add_balance(src, return_type.source.id, amount)
    elseif return_type.type == 'item' then
        if return_type.source.id == 'markedbills' then
            CORE.add_item(src, 'markedbills', 1, { worth = amount })
        else
            CORE.add_item(src, return_type.source.id, amount)
        end
    else
        return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '无效的来源类型。', duration = 5000 })
    end
    wash.capacity = wash.capacity - amount
    if wash.capacity == 0 then wash.is_washed = false end
    placed_objects[wash_id] = wash
    TriggerClientEvent('boii_moneywash:cl:sync_dui_data', -1, wash_id, { capacity = wash.capacity, is_washed = wash.is_washed })
    NOTIFICATIONS.send(src, { type = 'success', header = '洗钱机', message = ('已从洗钱机取出 $%d。'):format(amount), duration = 5000 })
end)

--- Starts the washing process.
--- @param wash_id number: Unique ID for the washer.
RegisterServerEvent('boii_moneywash:sv:start_washing', function(wash_id)
    local src, wash = source, placed_objects[wash_id]
    if not wash then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '无效的洗钱机ID。', duration = 5000 }) end
    if wash.capacity < config.defaults.max_capacity then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '洗钱机容量不足，无法开始洗钱。', duration = 5000 }) end
    if wash.is_washing then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '此洗钱机已在洗钱中。', duration = 5000 }) end
    local has_job = CORE.player_has_job(src, config.police.jobs, config.police.on_duty_only)
    if has_job then return NOTIFICATIONS.send(src, { type = 'error', header = '洗钱机', message = '您的职业不允许执行此操作，或您正在值班。', duration = 3500}) end
    wash.is_washing, wash.remaining_time = true, config.defaults.wash_time
    placed_objects[wash_id] = wash
    TriggerClientEvent('boii_moneywash:cl:sync_dui_data', -1, wash_id, { is_washing = true, remaining_time = wash.remaining_time })
    local interval = math.floor(wash.remaining_time / 4)
    CreateThread(function()
        while wash.remaining_time > 0 do
            Wait(1000)
            wash.remaining_time = wash.remaining_time - 1
            if wash.remaining_time % interval == 0 then
                if math.random(1, 100) <= config.police.alert_chance then
                    alert_police(wash.coords)
                end
            end
        end
        wash.dirty_in = (wash.dirty_in or 0) + wash.capacity
        wash.capacity = math.floor(wash.capacity * (config.defaults.return_value / 100))
        wash.clean_out = (wash.clean_out or 0) + wash.capacity
        wash.is_washing, wash.is_washed = false, true
        placed_objects[wash_id] = wash
        TriggerClientEvent('boii_moneywash:cl:sync_dui_data', -1, wash_id, { 
            is_washing = false, 
            capacity = wash.capacity, 
            is_washed = true, 
            dirty_in = wash.dirty_in, 
            clean_out = wash.clean_out 
        })
        NOTIFICATIONS.send(src, { type = 'success', header = '洗钱机', message = '洗钱完成。干净的钱现在可以收集了。', duration = 5000 })
    end)
end)

--- Updates a washer.
--- @param washer: Incoming washer data to update to.
RegisterNetEvent('boii_moneywash:sv:update_washer', function(washer)
    local src = source
    local id = washer.id
    if not has_permission(src) then print('you dont have permission') return end
    if placed_objects[id] then
        local updated_washer = placed_objects[id]
        updated_washer.take_value = washer.take_value
        updated_washer.return_value = washer.return_value
        updated_washer.max_capacity = washer.max_capacity
        updated_washer.max_limit = washer.max_limit
        updated_washer.limit_interval = washer.limit_interval
        updated_washer.wash_time = washer.wash_time
        placed_objects[id] = updated_washer
        MySQL.update('UPDATE money_washers SET take_value = @take_value, return_value = @return_value, max_capacity = @max_capacity, max_limit = @max_limit, limit_interval = @limit_interval, wash_time = @wash_time WHERE id = @id', {
            ['@take_value'] = washer.take_value,
            ['@return_value'] = washer.return_value,
            ['@max_capacity'] = washer.max_capacity,
            ['@max_limit'] = washer.max_limit,
            ['@limit_interval'] = washer.limit_interval,
            ['@wash_time'] = washer.wash_time,
            ['@id'] = id
        },
        function(affected_rows)
            if affected_rows > 0 then
                TriggerClientEvent('boii_moneywash:cl:sync_dui_data', -1, id, {
                    take_value = updated_washer.take_value,
                    return_value = updated_washer.return_value,
                    max_capacity = updated_washer.max_capacity,
                    max_limit = updated_washer.max_limit,
                    limit_interval = updated_washer.limit_interval,
                    wash_time = updated_washer.wash_time
                })
            else
                print(('[boii_moneywash] Failed to update Washer ID %d in the database.'):format(id))
            end
        end)
    else
        NOTIFICATIONS.send(src, {
            type = 'error',
            header = '洗钱机',
            message = ('未找到洗钱机ID %d。'):format(id),
            duration = 5000
        })
    end
end)

--- Disables a washer.
--- @param wash_id number: Unique ID for the washer.
RegisterServerEvent('boii_moneywash:sv:disable_washer', function(wash_id)
    local src, wash = source, placed_objects[wash_id]
    if not wash then return NOTIFICATIONS.send(src, { type = 'error', header = 'Money Wash', message = 'Invalid washer ID.', duration = 5000 }) end
    local has_job = CORE.player_has_job(src, config.police.jobs, config.police.on_duty_only)
    if not has_job then return NOTIFICATIONS.send(src, { type = 'error', header = 'Money Wash', message = 'You don\'t have the required job to do this or you are not on duty.', duration = 3500 }) end
    wash.is_disabled = true
    wash.disabled_at = os.time()
    MySQL.update('UPDATE money_washers SET is_washing = @is_washing, remaining_time = @remaining_time, is_disabled = @is_disabled WHERE id = @id', { 
        ['@is_washing'] = 0, 
        ['@remaining_time'] = 0,
        ['@is_disabled'] = 1,
        ['@id'] = wash_id 
    })
    TriggerClientEvent('boii_moneywash:cl:sync_dui_data', -1, wash_id, { is_disabled = true })
    NOTIFICATIONS.send(src, {
        type = 'success',
        header = 'Money Wash',
        message = 'You disabled a money wash.',
        duration = 3500
    })
end)

--- @section Threads

local SAVE_TIMER <const> = config.periodic_save_timer * 60 * 1000
local DISABLED_TIME <const> = config.police.disable_duration * 60

CreateThread(function()
    while true do
        Wait(SAVE_TIMER)
        for id, wash in pairs(placed_objects) do
            if wash.is_disabled and wash.disabled_at then
                local elapsed_time = os.time() - wash.disabled_at
                if elapsed_time >= DISABLED_TIME then
                    wash.is_disabled = false
                    wash.disabled_at = nil
                    TriggerClientEvent('boii_moneywash:cl:sync_dui_data', -1, id, { is_disabled = false })
                end
            end
            MySQL.update('UPDATE money_washers SET dirty_in = @dirty_in, clean_out = @clean_out, is_washing = @is_washing, remaining_time = @remaining_time, is_washed = @is_washed, capacity = @capacity, max_capacity = @max_capacity, max_limit = @max_limit, limit_used = @limit_used, limit_interval = @limit_interval, limit_hit_timestamp = @limit_hit_timestamp, is_disabled = @is_disabled WHERE id = @id', {
                ['@dirty_in'] = wash.dirty_in or 0,
                ['@clean_out'] = wash.clean_out or 0,
                ['@is_washing'] = wash.is_washing and 1 or 0,
                ['@remaining_time'] = wash.remaining_time or 0,
                ['@is_washed'] = wash.is_washed and 1 or 0,
                ['@capacity'] = wash.capacity,
                ['@max_capacity'] = wash.max_capacity or config.defaults.max_capacity,
                ['@max_limit'] = wash.max_limit or config.defaults.max_limit,
                ['@limit_used'] = wash.limit_used or 0,
                ['@limit_interval'] = wash.limit_interval or config.defaults.limit_interval,
                ['@limit_hit_timestamp'] = wash.limit_hit_timestamp or nil,
                ['@is_disabled'] = wash.is_disabled and 1 or 0,
                ['@id'] = id
            },
            function(affected_rows)
                if affected_rows > 0 then
                    print(('[boii_moneywash] Washer ID %s periodically saved.'):format(id))
                else
                    print(('[boii_moneywash] Failed to save Washer ID %s during periodic save.'):format(id))
                end
            end)
        end
    end
end)

--- @section Items

-- Function to handle the moneywash_disabler item
local function handle_moneywash_disabler(source)
    local player_ped = GetPlayerPed(source)
    local player_coords = GetEntityCoords(player_ped)
    local washers_in_range = {}
    for id, washer in pairs(placed_objects) do
        local dx, dy, dz = player_coords.x - washer.coords.x, player_coords.y - washer.coords.y, player_coords.z - washer.coords.z
        local distance = math.sqrt(dx * dx + dy * dy + dz * dz)
        if distance <= config.police.disable_range then
            washers_in_range[#washers_in_range + 1] = {
                id = id,
                coords = washer.coords,
                header = washer.header,
                is_disabled = washer.is_disabled
            }
        end
    end
    if #washers_in_range > 0 then
        TriggerClientEvent('boii_moneywash:cl:disable_tablet', source, washers_in_range)
    else
        NOTIFICATIONS.send(source, {
            type = 'error',
            header = 'Money Wash Disabler',
            message = 'No washers in range.',
            duration = 5000
        })
    end
end

-- Register the moneywash_disabler item
CORE.register_item('moneywash_disabler', function(source)
    handle_moneywash_disabler(source)
end)

--- @section Commands

--- 注册管理员命令
--- @param command string: 命令名称
--- @param description string: 命令描述
--- @param handler function: 命令处理函数
local function register_admin_command(command, description, handler)
    RegisterCommand(command, function(source, args, raw)
        if has_permission(source) then
            handler(source, args, raw)
        else
            -- 权限检查已在 has_permission 函数中处理错误消息
        end
    end, false)

    -- 添加命令建议
    TriggerEvent('chat:addSuggestion', '/' .. command, description)
end

--- 打开洗钱机配置菜单
register_admin_command('moneywash:admin', '打开洗钱机配置菜单', function(source, args, raw)
    if next(placed_objects) == nil then
        NOTIFICATIONS.send(source, {
            type = 'error',
            header = 'Money Wash',
            message = 'No washers have been placed, place one before trying to configure.',
            duration = 5000
        })
        return
    end
    local washers = {}
    for id, washer in pairs(placed_objects) do
        washers[#washers + 1] = {
            id = id,
            take_value = washer.take_value or config.defaults.take_value,
            return_value = washer.return_value or config.defaults.return_value,
            max_capacity = washer.max_capacity or config.defaults.max_capacity,
            max_limit = washer.max_limit or config.defaults.max_limit,
            limit_interval = washer.limit_interval or config.defaults.limit_interval,
            wash_time = washer.wash_time or config.defaults.wash_time,
            coords = washer.coords or {}
        }
    end
    TriggerClientEvent('boii_moneywash:cl:admin', source, washers)
end)

--- 放置新的洗钱机
register_admin_command('moneywash:place_washer', '放置一个新的洗钱机', function(source, args, raw)
    local player_ped = GetPlayerPed(source)
    local player_coords = GetEntityCoords(player_ped)
    local next_id = MySQL.query.await('SELECT IFNULL(MAX(id), 0) + 1 AS next_id FROM money_washers')[1].next_id
    local placement_data = {
        model = config.prop.model,
        coords = player_coords,
        id = next_id,
        header = config.prop.header,
        icon = config.prop.icon,
        keys = config.actions,
        outline = config.prop.outline,
        dirty_in = 0,
        clean_out = 0,
        capacity = 0,
        max_capacity = config.defaults.max_capacity,
        limit = config.defaults.limit,
        limit_used = 0,
        limit_interval = config.defaults.limit_interval,
        is_washing = false,
        is_disabled = false,
        remaining_time = 0
    }
    TriggerClientEvent('boii_moneywash:cl:start_object_placement', source, placement_data)
end)